codeunit 60006 "Package Split Management SMK"
{
    procedure ProcessPackageNo(var PackageSplitHeader: Record "Package Split Header SMK")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageSplitHeader."Package No." = '' then
            exit;

        PackageNoInformation.SetRange("Package No.", PackageSplitHeader."Package No.");
        PackageNoInformation.FindFirst();

        PackageSplitChecks(PackageNoInformation);

        PackageNoInformation.CalcFields(Inventory);
        PackageSplitHeader."Source Package Quantity" := PackageNoInformation.Inventory;
        PackageSplitHeader."Source Package Remaining Qty." := PackageNoInformation.Inventory;
        //PackageSplitHeader."Requested Hose Lenght" := PackageNoInformation."Hose Lenght SMK";

        // if PackageNoInformation."Package Type SMK" = PackageNoInformation."Package Type SMK"::Head then
        //CreatePackageSplitLinesForRequestedHoseLenght(PackageSplitHeader, PackageNoInformation);
    end;

    local procedure PackageSplitChecks(var PackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl SMK";
        //CanNotSplitPalettesErr: Label 'You can not split palettes.';
        CombinedShipmentErr: Label 'You can not split a package that is read in Combined Shipment.';
    begin
        // PackageNoInformation.TestField("Parent Package No. SMK", '');

        CombinedShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(CombinedShipmentErr);

        PackageNoInformation.CalcFields(Inventory);
        PackageNoInformation.TestField(Inventory);

        // if PackageNoInformation."Package Type SMK" = PackageNoInformation."Package Type SMK"::Palette then
        //     Error(CanNotSplitPalettesErr);
    end;

    // procedure CreatePackageSplitLinesForRequestedHoseLenght(PackageSplitHeader: Record "Package Split Header SMK"; PackageNoInformation: Record "Package No. Information")
    // var
    //     PackageSplitLine: Record "Package Split Line SMK";
    //     NoSeriesManagement: Codeunit "No. Series";
    //     TotalNewPackagesToCreate: Integer;
    //     CreatedPackageCount: Integer;
    // begin
    //     InventorySetup.Get();
    //     InventorySetup.TestField("Package Nos.");

    //     TotalNewPackagesToCreate := PackageSplitHeader."Total Quantity to Split" / PackageSplitHeader."Requested Hose Lenght";
    //     CreatedPackageCount := 0;

    //     //PackageNoInformation.SetAutoCalcFields("Location Code SMK");
    //     PackageNoInformation.CalcFields("Location Code SMK");

    //     repeat
    //         PackageSplitLine.Init();
    //         PackageSplitLine."Document No." := PackageSplitHeader."No.";
    //         PackageSplitLine."New Package No." := NoSeriesManagement.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true);
    //         PackageSplitLine."Item No." := PackageNoInformation."Item No.";
    //         PackageSplitLine."Item Description" := PackageNoInformation.Description;
    //         PackageSplitLine.Quantity := PackageSplitHeader."Requested Hose Lenght";
    //         PackageSplitLine."Lot No." := PackageNoInformation."Lot No. SMK";
    //         PackageSplitLine."Location Code" := PackageNoInformation."Location Code SMK";
    //         PackageSplitLine.Insert(true);
    //         CreatedPackageCount += 1;
    //     until CreatedPackageCount = TotalNewPackagesToCreate;
    // end;

    procedure CreateItemReclassificationJournalLinesFromPackageSplitHeader(var PackageSplitHeader: Record "Package Split Header SMK")
    var
        PackageSplitLine: Record "Package Split Line SMK";
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        PackageNoInformation: Record "Package No. Information";
        QtyErr: Label 'New Package Quantity can not be greater than Source Package Quantity.';
        NoSplitLineErr: Label 'No Package Split Line found.';
        ItemJournalLineLineNo: Integer;
    begin
        PackageSplitHeader.TestField(Completed, false);

        SumikaSetup.GetRecordOnce();

        if PackageSplitHeader."Source Package Quantity" < PackageSplitHeader."New Package Quantity" then
            Error(QtyErr);

        LastItemJournalLine.SetRange("Journal Template Name", SumikaSetup."Package Split Jnl. Tmpl. Name");
        LastItemJournalLine.SetRange("Journal Batch Name", SumikaSetup."Package Split Jnl. Batch Name");
        if LastItemJournalLine.FindLast() then
            ItemJournalLineLineNo := LastItemJournalLine."Line No." + 10000
        else
            ItemJournalLineLineNo := 10000;

        PackageSplitLine.SetAutoCalcFields("Source Package No.");
        PackageSplitLine.SetRange("Document No.", PackageSplitHeader."No.");
        if not PackageSplitLine.FindSet(false) then
            Error(NoSplitLineErr);
        repeat
            PackageNoInformation.Get(PackageSplitLine."Item No.", PackageSplitLine."Variant Code", PackageSplitLine."Source Package No.");

            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := SumikaSetup."Package Split Jnl. Tmpl. Name";
            ItemJournalLine."Journal Batch Name" := SumikaSetup."Package Split Jnl. Batch Name";
            ItemJournalLine."Line No." := ItemJournalLineLineNo;
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Entry Type" := ItemJournalLine."Entry Type"::Transfer;
            ItemJournalLine.Validate("Item No.", PackageSplitLine."Item No.");
            ItemJournalLine.Validate("Variant Code", PackageSplitLine."Variant Code");
            ItemJournalLine.Validate("Location Code", PackageSplitLine."Location Code");
            ItemJournalLine.Validate("Bin Code", SumikaPackageTransMgt.GetBinCodeFromPackageNoInformation(PackageNoInformation));
            ItemJournalLine.Validate(Quantity, PackageSplitLine.Quantity);
            ItemJournalLine.Validate("Lot No.", PackageSplitLine."Lot No.");
            ItemJournalLine.Validate("Package No.", PackageSplitLine."New Package No.");
            // ItemJournalLine."New Lot No." := PackageSplitLine."Lot No.";
            // ItemJournalLine."New Package No." := PackageSplitLine."New Package No.";
            ItemJournalLine.Insert(true);
            LastItemJournalLine := ItemJournalLine;
            AssignLotAndPackageNoToItemJournalLine(ItemJournalLine, PackageSplitLine);
            ItemJournalLineLineNo += 10000;
            CreatePackageNoInformationFromOldPackageNoInformation(PackageSplitLine);
        until PackageSplitLine.Next() = 0;

        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

        PackageSplitHeader.Completed := true;
        PackageSplitHeader.Modify(true);
    end;

    local procedure AssignLotAndPackageNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; PackageSplitLine: Record "Package Split Line SMK")
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        PackageSplitLine.CalcFields("Source Package No.");

        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        TempReservEntry."Lot No." := PackageSplitLine."Lot No."; //use Serial No. for SN
        TempReservEntry."Package No." := PackageSplitLine."Source Package No.";
        TempReservEntry.Quantity := ItemJournalLine.Quantity;
        TempReservEntry.Insert(false);

        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then //movement
            CreateReservEntry.SetNewTrackingFromItemJnlLine(ItemJournalLine);

        ItemJournalLine."Lot No." := '';
        ItemJournalLine."Package No." := '';
        ItemJournalLine.Modify(true);

        CreateReservEntry.CreateReservEntryFor(
          Database::"Item Journal Line", ItemJournalLine."Entry Type".AsInteger(),
          ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name", 0, ItemJournalLine."Line No.", ItemJournalLine."Qty. per Unit of Measure",
          TempReservEntry.Quantity, TempReservEntry.Quantity * ItemJournalLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(
          ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", '', 0D, ItemJournalLine."Posting Date", 0, ReservStatus::Prospect);
    end;

    local procedure CreatePackageNoInformationFromOldPackageNoInformation(PackageSplitLine: Record "Package Split Line SMK")
    var
        OldPackageNoInformation: Record "Package No. Information";
        NewPackageNoInformation: Record "Package No. Information";
    begin
        PackageSplitLine.CalcFields("Source Package No.");

        OldPackageNoInformation.Get(PackageSplitLine."Item No.", PackageSplitLine."Variant Code", PackageSplitLine."Source Package No.");

        NewPackageNoInformation.Init();
        NewPackageNoInformation.TransferFields(OldPackageNoInformation);
        NewPackageNoInformation."Package No." := PackageSplitLine."New Package No.";
        NewPackageNoInformation."Old Package No. SMK" := OldPackageNoInformation."Package No.";
        NewPackageNoInformation."Label Quantity SMK" := PackageSplitLine.Quantity;
        //NewPackageNoInformation."Package Type SMK" := NewPackageNoInformation."Package Type SMK"::Coil;
        NewPackageNoInformation.Insert(true);

        OldPackageNoInformation."Label Quantity SMK" -= PackageSplitLine.Quantity;
        OldPackageNoInformation.Modify(true);
    end;



    procedure CreateNewPackageSplitLineFromHeader(var PackageSplitHeader: Record "Package Split Header SMK"; PackageSplitLineQuantity: Decimal)
    var
        PackageSplitLine: Record "Package Split Line SMK";
        PackageNoInformation: Record "Package No. Information";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        InventorySetup.Get();
        InventorySetup.TestField("Package Nos.");

        PackageNoInformation.SetRange("Package No.", PackageSplitHeader."Package No.");
        PackageNoInformation.FindFirst();

        PackageNoInformation.CalcFields("Location Code SMK");

        PackageSplitLine.Init();
        PackageSplitLine."Document No." := PackageSplitHeader."No.";
        PackageSplitLine.Insert(true);
        PackageSplitLine."New Package No." := NoSeriesManagement.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true);
        PackageSplitLine."Item No." := PackageNoInformation."Item No.";
        PackageSplitLine."Variant Code" := PackageNoInformation."Variant Code";
        PackageSplitLine."Item Description" := PackageNoInformation.Description;
        PackageSplitLine."Lot No." := PackageNoInformation."Lot No. SMK";
        PackageSplitLine."Location Code" := PackageNoInformation."Location Code SMK";
        PackageSplitLine.Quantity := PackageSplitLineQuantity;
        PackageSplitLine.Modify(true);

        PackageSplitHeader."Source Package Remaining Qty." -= PackageSplitLineQuantity;
    end;

    procedure CreateMultiplePackagesFromPackageSplitHeader(var PackageSplitHeader: Record "Package Split Header SMK")
    var
        PackageSplitLineQty: Decimal;
    begin
        repeat
            if PackageSplitHeader."Source Package Remaining Qty." >= PackageSplitHeader."New Package Quantity" then
                PackageSplitLineQty := PackageSplitHeader."New Package Quantity"
            else
                PackageSplitLineQty := PackageSplitHeader."Source Package Remaining Qty.";

            CreateNewPackageSplitLineFromHeader(PackageSplitHeader, PackageSplitLineQty);

        until PackageSplitHeader."Source Package Remaining Qty." = 0;
    end;




    var
        SumikaSetup: Record "Sumika Setup SMK";
        InventorySetup: Record "Inventory Setup";
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
    //FlexatiProductionMngt: Codeunit "Flexati Production Mngt. SMK";
}