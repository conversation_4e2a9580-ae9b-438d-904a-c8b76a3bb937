page 60032 "Package Split SMK"
{
    ApplicationArea = All;
    Caption = 'Package Split';
    PageType = Card;
    SourceTable = "Package Split Header SMK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Completed;

                field("No."; Rec."No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Total Quantity to Split"; Rec."Source Package Quantity")
                {
                }
                field("Source Package Remaining Qty."; Rec."Source Package Remaining Qty.")
                {
                }
                field("Total Split Quantity"; Rec."New Package Quantity")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                // field("Requested Hose Lenght"; Rec."Requested Hose Lenght")
                // {
                //     ToolTip = 'Specifies the value of the Requested Hose Lenght field.';
                // }
            }
            group(LabelReadingArea)
            {
                Caption = 'Label Reading';
                Editable = not Rec.Completed;
                field("Package No."; Rec."Package No.")
                {
                    trigger OnAssistEdit()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetFilter(Inventory, '<>0');

                        if Page.RunModal(Page::"Package No. Information List", PackageNoInformation) = Action::LookupOK then
                            Rec.Validate("Package No.", PackageNoInformation."Package No.");
                    end;
                }
            }
            part(Lines; "Package Split Subpage SMK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec.Completed;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateSinglePackage)
            {
                ApplicationArea = All;
                Caption = 'Create Single Package';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                Image = PostInventoryToGL;
                ToolTip = 'Executes the Create New Package action.';
                trigger OnAction()
                begin
                    PackageSplitManagement.CreateNewPackageSplitLineFromHeader(Rec, Rec."New Package Quantity");
                end;
            }
            action(CreateMultiplePackages)
            {
                ApplicationArea = All;
                Caption = 'Create Multiple Packages';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                Image = Inventory;
                ToolTip = 'Executes the Create Multiple Packages action.';
                trigger OnAction()
                begin
                    PackageSplitManagement.CreateMultiplePackagesFromPackageSplitHeader(Rec);
                end;
            }
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Post action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    PackageSplitManagement.CreateItemReclassificationJournalLinesFromPackageSplitHeader(Rec);
                    //CurrPage.Close();
                end;
            }
            action("PackageLabel SMK")
            {
                ApplicationArea = All;
                Caption = 'Print All Package Labels';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Package Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PackageSplitLine: Record "Package Split Line SMK";
                begin
                    Rec.TestField(Completed, true);

                    PackageSplitLine.SetRange("Document No.", Rec."No.");
                    PackageSplitLine.FindSet(false);
                    repeat
                        PackageNoInformation.Get(PackageSplitLine."Item No.", PackageSplitLine."Variant Code", PackageSplitLine."New Package No.");
                        PackageNoInformation.Mark(true);
                    until PackageSplitLine.Next() = 0;

                    PackageNoInformation.MarkedOnly(true);
                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
        }
    }
    var
        PackageSplitManagement: Codeunit "Package Split Management SMK";
}