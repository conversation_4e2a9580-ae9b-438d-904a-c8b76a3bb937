report 60006 "Items by Lot and Loc.-Cost SMK"
{
    ApplicationArea = All;
    Caption = 'Items by Lot and Location - Cost';
    UsageCategory = ReportsAndAnalysis;
    DataAccessIntent = ReadOnly;

    dataset
    {
        dataitem(ItemLedgerEntry; "Item Ledger Entry")
        {
            RequestFilterFields = "Date Filter SMK", "Posting Date";

            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(ItemDescription; SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode(ItemLedgerEntry."Item No.", ''))
            {
            }
            column(CostAmtActualbyDateSMK_ItemLedgerEntry; "Cost Amt. (Actual) by Date SMK")
            {
            }
            column(PostingDate_ItemLedgerEntry; "Posting Date")
            {
            }
            column(CompanyName; CompanyNameText)
            {
            }
            column(LCYCode; LCYCodeText)
            {
            }
            column(ReportFilters; ReportFiltersText)
            {
            }
            column(dateFilter; "Date Filter SMK")
            {
            }
            column(EntryDate; GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry))
            {
            }

            trigger OnPreDataItem()
            begin
                // Set load fields for performance optimization - only essential fields for layout
                SetLoadFields("Item No.", "Variant Code", Description, "Location Code", "Lot No.",
                             Quantity, "Unit of Measure Code", "Posting Date", "Date Filter SMK");

                // Initialize values that are the same for all records (performance optimization)
                CompanyNameText := CompanyName();
                LCYCodeText := SumikaBasicFunctions.GetLCYCode();
                ReportFiltersText := GetFilters();
            end;
        }
    }

    local procedure GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry: Record "Item Ledger Entry"): Date
    var
        FirstEntryItemLedgerEntry: Record "Item Ledger Entry";
    begin
        // Optimized version with SetLoadFields for performance
        FirstEntryItemLedgerEntry.SetLoadFields("Posting Date");
        FirstEntryItemLedgerEntry.SetCurrentKey("Item No.", "Variant Code", "Lot No.", "Posting Date");

        FirstEntryItemLedgerEntry.SetRange(Positive, true);
        FirstEntryItemLedgerEntry.SetRange("Item No.", ItemLedgerEntry."Item No.");
        FirstEntryItemLedgerEntry.SetRange("Variant Code", ItemLedgerEntry."Variant Code");
        FirstEntryItemLedgerEntry.SetRange("Lot No.", ItemLedgerEntry."Lot No.");

        if FirstEntryItemLedgerEntry.FindFirst() then
            exit(FirstEntryItemLedgerEntry."Posting Date")
        else
            exit(0D);
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
        CompanyNameText: Text;
        LCYCodeText: Code[10];
        ReportFiltersText: Text;
}