report 60006 "Items by Lot and Loc.-Cost SMK"
{
    ApplicationArea = All;
    Caption = 'Items by Lot and Location - Cost';
    UsageCategory = ReportsAndAnalysis;
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             field(ViewCostAmounts; ViewCostAmounts)
    //             {
    //                 ApplicationArea = All;
    //             }
    //         }

    //     }
    // }
    dataset
    {
        dataitem(ItemLedgerEntry; "Item Ledger Entry")
        {
            RequestFilterFields = "Date Filter SMK", "Posting Date";

            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(ItemDescription; SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode(ItemLedgerEntry."Item No.", ''))
            {
            }
            column(CostAmtActualbyDateSMK_ItemLedgerEntry; "Cost Amt. (Actual) by Date SMK")
            {
            }
            column(EntryDate; GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry))
            {
            }
            column(CompanyName; CompanyName())
            {
            }
            column(LCYCode; SumikaBasicFunctions.GetLCYCode())
            {
            }
            column(ReportFilters; ItemLedgerEntry.GetFilters())
            {
            }
            column(PostingDate_ItemLedgerEntry; "Posting Date")
            {
            }
            column(dateFilter; "Date Filter SMK")
            {
            }
            column(AppliedEntrytoAdjust_ItemLedgerEntry; "Applied Entry to Adjust")
            {
            }
            column(AppliestoEntry_ItemLedgerEntry; "Applies-to Entry")
            {
            }
            column(Area_ItemLedgerEntry; "Area")
            {
            }
            column(AssembletoOrder_ItemLedgerEntry; "Assemble to Order")
            {
            }
            column(CompletelyInvoiced_ItemLedgerEntry; "Completely Invoiced")
            {
            }
            column(Correction_ItemLedgerEntry; Correction)
            {
            }
            column(CostAmountActual_ItemLedgerEntry; "Cost Amount (Actual)")
            {
            }
            column(CostAmountActualACY_ItemLedgerEntry; "Cost Amount (Actual) (ACY)")
            {
            }
            column(CostAmountExpected_ItemLedgerEntry; "Cost Amount (Expected)")
            {
            }
            column(CostAmountExpectedACY_ItemLedgerEntry; "Cost Amount (Expected) (ACY)")
            {
            }
            column(CostAmountNonInvtbl_ItemLedgerEntry; "Cost Amount (Non-Invtbl.)")
            {
            }
            column(CostAmountNonInvtblACY_ItemLedgerEntry; "Cost Amount (Non-Invtbl.)(ACY)")
            {
            }
            column(CountryRegionCode_ItemLedgerEntry; "Country/Region Code")
            {
            }
            column(DerivedfromBlanketOrder_ItemLedgerEntry; "Derived from Blanket Order")
            {
            }
            column(DimensionSetID_ItemLedgerEntry; "Dimension Set ID")
            {
            }
            column(DocumentDate_ItemLedgerEntry; "Document Date")
            {
            }
            column(DocumentLineNo_ItemLedgerEntry; "Document Line No.")
            {
            }
            column(DocumentNo_ItemLedgerEntry; "Document No.")
            {
            }
            column(DocumentType_ItemLedgerEntry; "Document Type")
            {
            }
            column(DropShipment_ItemLedgerEntry; "Drop Shipment")
            {
            }
            column(EntryNo_ItemLedgerEntry; "Entry No.")
            {
            }
            column(EntryType_ItemLedgerEntry; "Entry Type")
            {
            }
            column(EntryExitPoint_ItemLedgerEntry; "Entry/Exit Point")
            {
            }
            column(ExpirationDate_ItemLedgerEntry; "Expiration Date")
            {
            }
            column(ExternalDocumentNo_ItemLedgerEntry; "External Document No.")
            {
            }
            column(GlobalDimension1Code_ItemLedgerEntry; "Global Dimension 1 Code")
            {
            }
            column(GlobalDimension2Code_ItemLedgerEntry; "Global Dimension 2 Code")
            {
            }
            column(InvoicedQuantity_ItemLedgerEntry; "Invoiced Quantity")
            {
            }
            column(ItemCategoryCode_ItemLedgerEntry; "Item Category Code")
            {
            }
            column(ItemCategoryCodeFFSMK_ItemLedgerEntry; "Item Category Code FF SMK")
            {
            }
            column(ItemReferenceNo_ItemLedgerEntry; "Item Reference No.")
            {
            }
            column(ItemTracking_ItemLedgerEntry; "Item Tracking")
            {
            }
            column(JobNo_ItemLedgerEntry; "Job No.")
            {
            }
            column(JobPurchase_ItemLedgerEntry; "Job Purchase")
            {
            }
            column(JobTaskNo_ItemLedgerEntry; "Job Task No.")
            {
            }
            column(LastInvoiceDate_ItemLedgerEntry; "Last Invoice Date")
            {
            }
            column(NoSeries_ItemLedgerEntry; "No. Series")
            {
            }
            column(Nonstock_ItemLedgerEntry; Nonstock)
            {
            }
            column(Open_ItemLedgerEntry; Open)
            {
            }
            column(OrderLineNo_ItemLedgerEntry; "Order Line No.")
            {
            }
            column(OrderNo_ItemLedgerEntry; "Order No.")
            {
            }
            column(OrderType_ItemLedgerEntry; "Order Type")
            {
            }
            column(OriginallyOrderedNo_ItemLedgerEntry; "Originally Ordered No.")
            {
            }
            column(OriginallyOrderedVarCode_ItemLedgerEntry; "Originally Ordered Var. Code")
            {
            }
            column(OutofStockSubstitution_ItemLedgerEntry; "Out-of-Stock Substitution")
            {
            }
            column(PackageNo_ItemLedgerEntry; "Package No.")
            {
            }
            column(ParentPackageNoSMK_ItemLedgerEntry; "Parent Package No. SMK")
            {
            }
            column(Positive_ItemLedgerEntry; Positive)
            {
            }
            column(ProdOrderCompLineNo_ItemLedgerEntry; "Prod. Order Comp. Line No.")
            {
            }
            column(PurchaseAmountActual_ItemLedgerEntry; "Purchase Amount (Actual)")
            {
            }
            column(PurchaseAmountExpected_ItemLedgerEntry; "Purchase Amount (Expected)")
            {
            }
            column(PurchasingCode_ItemLedgerEntry; "Purchasing Code")
            {
            }
            column(QtyperUnitofMeasure_ItemLedgerEntry; "Qty. per Unit of Measure")
            {
            }
            column(RemainingQuantity_ItemLedgerEntry; "Remaining Quantity")
            {
            }
            column(ReservedQuantity_ItemLedgerEntry; "Reserved Quantity")
            {
            }
            column(ReturnReasonCode_ItemLedgerEntry; "Return Reason Code")
            {
            }
            column(SalesAmountActual_ItemLedgerEntry; "Sales Amount (Actual)")
            {
            }
            column(SalesAmountExpected_ItemLedgerEntry; "Sales Amount (Expected)")
            {
            }
            column(SerialNo_ItemLedgerEntry; "Serial No.")
            {
            }
            column(ShippedQtyNotReturned_ItemLedgerEntry; "Shipped Qty. Not Returned")
            {
            }
            column(ShortcutDimension3Code_ItemLedgerEntry; "Shortcut Dimension 3 Code")
            {
            }
            column(ShortcutDimension4Code_ItemLedgerEntry; "Shortcut Dimension 4 Code")
            {
            }
            column(ShortcutDimension5Code_ItemLedgerEntry; "Shortcut Dimension 5 Code")
            {
            }
            column(ShortcutDimension6Code_ItemLedgerEntry; "Shortcut Dimension 6 Code")
            {
            }
            column(ShortcutDimension7Code_ItemLedgerEntry; "Shortcut Dimension 7 Code")
            {
            }
            column(ShortcutDimension8Code_ItemLedgerEntry; "Shortcut Dimension 8 Code")
            {
            }
            column(ShptMethodCode_ItemLedgerEntry; "Shpt. Method Code")
            {
            }
            column(SourceNo_ItemLedgerEntry; "Source No.")
            {
            }
            column(SourceType_ItemLedgerEntry; "Source Type")
            {
            }
            column(SystemCreatedAt_ItemLedgerEntry; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy_ItemLedgerEntry; SystemCreatedBy)
            {
            }
            column(SystemId_ItemLedgerEntry; SystemId)
            {
            }
            column(SystemModifiedAt_ItemLedgerEntry; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy_ItemLedgerEntry; SystemModifiedBy)
            {
            }
            column(TransactionSpecification_ItemLedgerEntry; "Transaction Specification")
            {
            }
            column(TransactionType_ItemLedgerEntry; "Transaction Type")
            {
            }
            column(TransportMethod_ItemLedgerEntry; "Transport Method")
            {
            }
            column(VendorLotNoSMK_ItemLedgerEntry; "Vendor Lot No. SMK")
            {
            }
            column(WarrantyDate_ItemLedgerEntry; "Warranty Date")
            {
            }
            trigger OnPreDataItem()
            begin
                Message(ItemLedgerEntry.GetFilters());
            end;
        }
    }
    local procedure GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry: Record "Item Ledger Entry"): Date
    var
        FirstEntryItemLedgerEntry: Record "Item Ledger Entry";
    begin
        FirstEntryItemLedgerEntry.SetCurrentKey("Posting Date");

        FirstEntryItemLedgerEntry.SetRange(Positive, true);
        FirstEntryItemLedgerEntry.SetRange("Item No.", ItemLedgerEntry."Item No.");
        FirstEntryItemLedgerEntry.SetRange("Variant Code", ItemLedgerEntry."Variant Code");
        FirstEntryItemLedgerEntry.SetRange("Lot No.", ItemLedgerEntry."Lot No.");

        if FirstEntryItemLedgerEntry.FindFirst() then
            exit(FirstEntryItemLedgerEntry."Posting Date")
        else
            exit(0D);
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}